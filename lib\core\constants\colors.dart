import 'package:flutter/material.dart';

/// 颜色常量定义
class AppColors {
  AppColors._();

  // 日间主题颜色
  static const Color lightPrimaryText = Color(0xFF2C2C2C); // 墨色
  static const Color lightSecondaryText = Color(0xFF595959); // 次级文字
  static const Color lightAccentColor = Color(0xFF3B8BFA); // 活力蓝
  static const Color lightBackgroundColor = Color(0xFFFAF8F2); // 宣纸白
  static const Color lightSurfaceColor = Colors.white;
  static const Color lightBorderColor = Color(0xFFE0E0E0);

  // 夜间主题颜色
  static const Color darkPrimaryText = Color(0xFFE5E5E5); // 浅灰色文字
  static const Color darkSecondaryText = Color(0xFFB0B0B0); // 次要文字
  static const Color darkAccentColor = Color(0xFF3B8BFA); // 保持活力蓝
  static const Color darkBackgroundColor = Color(0xFF1A1A1A); // 深色背景
  static const Color darkSurfaceColor = Color(0xFF2D2D2D); // 卡片/表面颜色
  static const Color darkBorderColor = Color(0xFF3A3A3A); // 边框颜色

  // 功能色彩
  static const Color successColor = Color(0xFF10B981);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color infoColor = Color(0xFF3B82F6);

  // Agent 颜色
  static const Color agentBaziMaster = Color(0xFF6B46C1);
  static const Color agentCareer = Color(0xFF2563EB);
  static const Color agentEmotion = Color(0xFFEC4899);
  static const Color agentHealth = Color(0xFF10B981);

  // 透明度定义
  static const double lowOpacity = 0.1;
  static const double mediumOpacity = 0.3;
  static const double highOpacity = 0.7;
}