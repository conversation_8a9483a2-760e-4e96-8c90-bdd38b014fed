import 'package:flutter/material.dart';
import '../../../../core/constants/dimensions.dart';
import '../../data/models/bazi_option.dart';

/// 八字选择器组件
class BaziSelector extends StatelessWidget {
  final List<String> selectedBaziIds;
  final List<BaziOption> baziOptions;
  final Function(String) onBaziSelected;
  final bool isMultiSelectMode;

  const BaziSelector({
    super.key,
    required this.selectedBaziIds,
    required this.baziOptions,
    required this.onBaziSelected,
    this.isMultiSelectMode = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: () => _showBaziSelector(context),
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingL,
          vertical: AppDimensions.paddingM,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: theme.dividerColor),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_month,
              color: theme.colorScheme.primary,
              size: 20,
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '八字选项',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _getBaziSummary(),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            if (selectedBaziIds.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Text(
                  '${selectedBaziIds.length}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            const SizedBox(width: AppDimensions.paddingS),
            Icon(
              Icons.keyboard_arrow_down,
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ],
        ),
      ),
    );
  }

  String _getBaziSummary() {
    if (selectedBaziIds.isEmpty) {
      return '请选择八字选项';
    }
    
    final selectedOptions = baziOptions
        .where((option) => selectedBaziIds.contains(option.id))
        .toList();
    
    if (selectedOptions.isEmpty) {
      return '已选择 ${selectedBaziIds.length} 个选项';
    }
    
    if (selectedOptions.length == 1) {
      return selectedOptions.first.name;
    }
    
    return '${selectedOptions.first.name} 等 ${selectedOptions.length} 个';
  }

  void _showBaziSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      builder: (context) => _BaziSelectorSheet(
        selectedBaziIds: selectedBaziIds,
        baziOptions: baziOptions,
        isMultiSelectMode: isMultiSelectMode,
        onBaziSelected: (baziId) {
          onBaziSelected(baziId);
          if (!isMultiSelectMode) {
            Navigator.pop(context);
          }
        },
      ),
    );
  }
}

class _BaziSelectorSheet extends StatefulWidget {
  final List<String> selectedBaziIds;
  final List<BaziOption> baziOptions;
  final bool isMultiSelectMode;
  final Function(String) onBaziSelected;

  const _BaziSelectorSheet({
    required this.selectedBaziIds,
    required this.baziOptions,
    required this.isMultiSelectMode,
    required this.onBaziSelected,
  });

  @override
  State<_BaziSelectorSheet> createState() => _BaziSelectorSheetState();
}

class _BaziSelectorSheetState extends State<_BaziSelectorSheet> {
  late List<String> _tempSelectedIds;

  @override
  void initState() {
    super.initState();
    _tempSelectedIds = List.from(widget.selectedBaziIds);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              Text(
                '选择八字',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (widget.isMultiSelectMode)
                TextButton(
                  onPressed: () {
                    for (final id in _tempSelectedIds) {
                      widget.onBaziSelected(id);
                    }
                    Navigator.pop(context);
                  },
                  child: const Text('确定'),
                ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          // 快速操作按钮
          if (widget.isMultiSelectMode) ...[
            Row(
              children: [
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _tempSelectedIds.clear();
                    });
                  },
                  icon: const Icon(Icons.clear_all),
                  label: const Text('全部清除'),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _tempSelectedIds = widget.baziOptions.map((o) => o.id).toList();
                    });
                  },
                  icon: const Icon(Icons.select_all),
                  label: const Text('全部选择'),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingL),
          ],
          
          // 八字选项列表
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                children: widget.baziOptions.map((option) => _buildBaziTile(context, option)).toList(),
              ),
            ),
          ),
          
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildBaziTile(BuildContext context, BaziOption option) {
    final theme = Theme.of(context);
    final isSelected = _tempSelectedIds.contains(option.id);

    return InkWell(
      onTap: () {
        if (widget.isMultiSelectMode) {
          setState(() {
            if (isSelected) {
              _tempSelectedIds.remove(option.id);
            } else {
              _tempSelectedIds.add(option.id);
            }
          });
        } else {
          widget.onBaziSelected(option.id);
        }
      },
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        margin: const EdgeInsets.only(bottom: AppDimensions.paddingS),
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        decoration: BoxDecoration(
          color: isSelected 
              ? theme.colorScheme.primaryContainer.withOpacity(0.3)
              : Colors.transparent,
          border: Border.all(
            color: isSelected 
                ? theme.colorScheme.primary
                : theme.dividerColor,
          ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected 
                          ? theme.colorScheme.primary
                          : null,
                    ),
                  ),
                  if (option.description.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      option.description,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                  const SizedBox(height: 8),
                  _buildBaziInfo(context, option),
                ],
              ),
            ),
            if (widget.isMultiSelectMode)
              Checkbox(
                value: isSelected,
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      _tempSelectedIds.add(option.id);
                    } else {
                      _tempSelectedIds.remove(option.id);
                    }
                  });
                },
              )
            else if (isSelected)
              Icon(
                Icons.check_circle,
                color: theme.colorScheme.primary,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBaziInfo(BuildContext context, BaziOption option) {
    final theme = Theme.of(context);
    
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: [
        _buildInfoChip(context, '生日', '${option.birthYear}年${option.birthMonth}月${option.birthDay}日'),
        _buildInfoChip(context, '时辰', option.birthHour),
        _buildInfoChip(context, '性别', option.gender == 'male' ? '男' : '女'),
      ],
    );
  }

  Widget _buildInfoChip(BuildContext context, String label, String value) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Text(
        '$label: $value',
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }
}