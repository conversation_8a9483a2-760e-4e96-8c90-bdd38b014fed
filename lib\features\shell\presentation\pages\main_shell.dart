import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/platform/custom_title_bar.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../../../bazi/presentation/pages/bazi_chart_page.dart';
import '../../../profile/presentation/pages/profile_page.dart';
import '../../../chat/presentation/pages/chat_page.dart';
import '../widgets/responsive_navigation.dart';
import '../providers/navigation_provider.dart';

/// 主应用壳层
class MainShell extends StatefulWidget {
  const MainShell({super.key});

  @override
  State<MainShell> createState() => _MainShellState();
}

class _MainShellState extends State<MainShell> {
  @override
  void initState() {
    super.initState();
    
    // 初始化导航状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!context.read<NavigationProvider>().navigationItems.any((item) => item.id == 'chat')) {
        // 如果还没有初始化导航项，这里可以添加初始化逻辑
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => NavigationProvider(),
      child: Consumer<NavigationProvider>(
        builder: (context, navigationProvider, child) {
          final pages = _getPages();
          final selectedIndex = navigationProvider.selectedIndex;
          
          // 小屏幕布局（移动端）- 底部导航栏
          if (ResponsiveUtils.isMobile(context)) {
            return Scaffold(
              body: pages.elementAt(selectedIndex),
              bottomNavigationBar: ResponsiveNavigation(
                selectedIndex: selectedIndex,
                onDestinationSelected: navigationProvider.selectIndex,
              ),
            );
          } else {
            // 桌面端布局 - 顶部标题栏 + 侧边导航栏
            return Scaffold(
              appBar: const CustomTitleBar(
                title: '八字命理',
                showBackButton: false,
              ),
              body: Row(
                children: <Widget>[
                  ResponsiveNavigation(
                    selectedIndex: selectedIndex,
                    onDestinationSelected: navigationProvider.selectIndex,
                  ),
                  Expanded(
                    child: pages.elementAt(selectedIndex),
                  ),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  List<Widget> _getPages() {
    return const [
      ChatPage(),
      BaziChartPage(),
      ProfilePage(),
    ];
  }
}