import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/platform/custom_title_bar.dart';
import '../../../../core/extensions/context_extensions.dart';
import '../../../../core/constants/dimensions.dart';
import '../../../../shared/widgets/privacy_policy_page.dart';
import '../../../../core/utils/ui_helpers.dart';
import '../providers/auth_provider.dart';
import '../widgets/auth_form.dart';
import 'register_page.dart';
import 'forgot_password_page.dart';
import '../../../shell/presentation/pages/main_shell.dart';

/// 登录页面
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: const CustomTitleBar(
        title: '八字命理 - 登录',
        showBackButton: false,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
                child: Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        // Logo
                        Icon(
                          Icons.brightness_7_outlined,
                          size: 60,
                          color: theme.textTheme.displayMedium?.color,
                        ),
                        const SizedBox(height: 24),
                        // Title
                        Text(
                          '登录',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.displayMedium,
                        ),
                        const SizedBox(height: 12),
                        // Description
                        Text(
                          '开启您的命理探索之旅',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.bodyMedium,
                        ),
                        const SizedBox(height: AppDimensions.paddingXXL),
                        
                        // 使用认证表单组件
                        AuthFormWidget(
                          formType: AuthFormType.login,
                          isLoading: authProvider.isLoading,
                          errorMessage: authProvider.error,
                          onSubmit: () => _handleLogin(authProvider),
                        ),
                        
                        const SizedBox(height: AppDimensions.paddingXXL),
                        // Register Link
                        Center(
                          child: RichText(
                            text: TextSpan(
                              text: '还没有账户? ',
                              style: theme.textTheme.bodyMedium,
                              children: <TextSpan>[
                                TextSpan(
                                  text: '立即注册',
                                  style: TextStyle(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      Navigator.of(context).push(
                                        MaterialPageRoute(builder: (context) => const RegisterPage()),
                                      );
                                    },
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: AppDimensions.paddingXXXL),
                        // Privacy Policy
                        Center(
                          child: RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              text: '登录即代表您同意我们的 ',
                              style: theme.textTheme.bodySmall?.copyWith(fontSize: 12),
                              children: <TextSpan>[
                                TextSpan(
                                  text: '隐私协议',
                                  style: TextStyle(
                                    color: theme.colorScheme.secondary,
                                    decoration: TextDecoration.underline,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      UiHelpers.showAppDialog(context, const PrivacyPolicyPage());
                                    },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleLogin(AuthProvider authProvider) async {
    if (_formKey.currentState?.validate() ?? false) {
      final success = await authProvider.login(
        phone: '13800138000', // 示例数据
        password: '123456',
        rememberMe: true,
      );

      if (success && mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainShell()),
        );
      }
    }
  }

  void _navigateToForgotPassword() {
    // TODO: 导航到忘记密码页面
    context.showSnackBar('忘记密码功能正在开发中');
  }
}