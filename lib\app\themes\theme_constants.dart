import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';

/// 主题常量定义
class ThemeConstants {
  ThemeConstants._();

  // 字体大小
  static const double fontSizeXS = 10.0;
  static const double fontSizeS = 12.0;
  static const double fontSizeM = 14.0;
  static const double fontSizeL = 16.0;
  static const double fontSizeXL = 18.0;
  static const double fontSizeXXL = 20.0;
  static const double fontSizeXXXL = 24.0;
  static const double fontSizeTitle = 28.0;
  static const double fontSizeDisplay = 32.0;

  // 字体权重
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;

  // 阴影配置
  static const List<BoxShadow> lightCardShadow = [
    BoxShadow(
      color: Color(0x08000000),
      offset: Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> darkCardShadow = [
    BoxShadow(
      color: Color(0x30000000),
      offset: Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: Color(0x40000000),
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: 0,
    ),
  ];

  // 颜色方案
  static const ColorScheme lightColorScheme = ColorScheme.light(
    primary: AppColors.lightAccentColor,
    secondary: AppColors.lightPrimaryText,
    surface: AppColors.lightBackgroundColor,
    background: AppColors.lightBackgroundColor,
    onPrimary: Colors.white,
    onSecondary: AppColors.lightPrimaryText,
    onSurface: AppColors.lightPrimaryText,
    onBackground: AppColors.lightPrimaryText,
    error: AppColors.errorColor,
    onError: Colors.white,
  );

  static const ColorScheme darkColorScheme = ColorScheme.dark(
    primary: AppColors.darkAccentColor,
    secondary: AppColors.darkPrimaryText,
    surface: AppColors.darkBackgroundColor,
    background: AppColors.darkBackgroundColor,
    onPrimary: Colors.white,
    onSecondary: AppColors.darkPrimaryText,
    onSurface: AppColors.darkPrimaryText,
    onBackground: AppColors.darkPrimaryText,
    error: AppColors.errorColor,
    onError: Colors.white,
  );

  // 边框样式
  static const BorderSide lightBorderSide = BorderSide(
    color: AppColors.lightBorderColor,
    width: 1.0,
  );

  static const BorderSide darkBorderSide = BorderSide(
    color: AppColors.darkBorderColor,
    width: 1.0,
  );

  // 焦点边框样式
  static const BorderSide focusedBorderSide = BorderSide(
    color: AppColors.lightAccentColor,
    width: 1.5,
  );

  // 圆角样式
  static const BorderRadius smallBorderRadius = BorderRadius.all(Radius.circular(4));
  static const BorderRadius mediumBorderRadius = BorderRadius.all(Radius.circular(8));
  static const BorderRadius largeBorderRadius = BorderRadius.all(Radius.circular(12));
  static const BorderRadius extraLargeBorderRadius = BorderRadius.all(Radius.circular(16));
  static const BorderRadius roundBorderRadius = BorderRadius.all(Radius.circular(20));

  // 动画曲线
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve fastCurve = Curves.easeOut;
  static const Curve slowCurve = Curves.easeInOutCubic;
}